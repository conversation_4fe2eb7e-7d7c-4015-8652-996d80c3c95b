class GpsHistoryPoint {
  final String id;
  final double lat;
  final double lng;
  final double speed;
  final DateTime createdAt;
  final String deviceNumber;
  final String? address;
  final Map<String, dynamic>? payload;

  GpsHistoryPoint({
    required this.id,
    required this.lat,
    required this.lng,
    required this.speed,
    required this.createdAt,
    required this.deviceNumber,
    this.address,
    this.payload,
  });

  factory GpsHistoryPoint.fromJson(Map<String, dynamic> json) {
    return GpsHistoryPoint(
      id: json['_id']?.toString() ?? '',
      lat: (json['lat'] ?? 0.0).toDouble(),
      lng: (json['lng'] ?? 0.0).toDouble(),
      speed: (json['speed'] ?? 0.0).toDouble(),
      createdAt:
          DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      deviceNumber: json['deviceNumber']?.toString() ?? '',
      address: json['address']?.toString(),
      payload: json['payload'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'lat': lat,
      'lng': lng,
      'speed': speed,
      'createdAt': createdAt.toIso8601String(),
      'deviceNumber': deviceNumber,
      'address': address,
      'payload': payload,
    };
  }

  bool get hasValidCoordinates => lat != 0.0 && lng != 0.0;

  String get formattedSpeed => '${speed.toStringAsFixed(1)} km/h';

  String get formattedTime {
    final hour = createdAt.hour.toString().padLeft(2, '0');
    final minute = createdAt.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  String get formattedDateTime {
    final year = createdAt.year;
    final month = createdAt.month.toString().padLeft(2, '0');
    final day = createdAt.day.toString().padLeft(2, '0');
    final hour = createdAt.hour.toString().padLeft(2, '0');
    final minute = createdAt.minute.toString().padLeft(2, '0');
    return '$year-$month-$day $hour:$minute';
  }

  String get coordinatesString =>
      '${lat.toStringAsFixed(6)}, ${lng.toStringAsFixed(6)}';
}

class GpsHistoryResponse {
  final bool success;
  final List<GpsHistoryPoint> logs;
  final int totalFound;
  final int validCoordinates;
  final int deduplicatedPoints;
  final int duplicatesRemoved;
  final String source;
  final String? error;

  GpsHistoryResponse({
    required this.success,
    required this.logs,
    required this.totalFound,
    required this.validCoordinates,
    required this.deduplicatedPoints,
    required this.duplicatesRemoved,
    required this.source,
    this.error,
  });

  factory GpsHistoryResponse.fromJson(Map<String, dynamic> json) {
    final logsList = json['logs'] as List<dynamic>? ?? [];
    final gpsPoints = logsList
        .map((log) => GpsHistoryPoint.fromJson(log as Map<String, dynamic>))
        .toList();

    return GpsHistoryResponse(
      success: json['success'] ?? false,
      logs: gpsPoints,
      totalFound: json['totalFound'] ?? 0,
      validCoordinates: json['validCoordinates'] ?? 0,
      deduplicatedPoints: json['deduplicatedPoints'] ?? 0,
      duplicatesRemoved: json['duplicatesRemoved'] ?? 0,
      source: json['source']?.toString() ?? 'unknown',
      error: json['error']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'logs': logs.map((log) => log.toJson()).toList(),
      'totalFound': totalFound,
      'validCoordinates': validCoordinates,
      'deduplicatedPoints': deduplicatedPoints,
      'duplicatesRemoved': duplicatesRemoved,
      'source': source,
      'error': error,
    };
  }

  bool get hasData => logs.isNotEmpty;
  bool get hasValidData => logs.any((point) => point.hasValidCoordinates);

  List<GpsHistoryPoint> get validPoints =>
      logs.where((point) => point.hasValidCoordinates).toList();

  String get statsString {
    return 'Total: $totalFound, Valid: $validCoordinates, Deduplicated: $deduplicatedPoints';
  }
}

class GpsHistoryFilter {
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? deviceNumber;
  final double? minSpeed;
  final double? maxSpeed;

  GpsHistoryFilter({
    this.fromDate,
    this.toDate,
    this.deviceNumber,
    this.minSpeed,
    this.maxSpeed,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};

    if (fromDate != null) {
      json['from'] = fromDate!.toIso8601String();
    }

    if (toDate != null) {
      json['to'] = toDate!.toIso8601String();
    }

    if (deviceNumber != null) {
      json['deviceNumber'] = deviceNumber;
    }

    return json;
  }

  GpsHistoryFilter copyWith({
    DateTime? fromDate,
    DateTime? toDate,
    String? deviceNumber,
    double? minSpeed,
    double? maxSpeed,
  }) {
    return GpsHistoryFilter(
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      deviceNumber: deviceNumber ?? this.deviceNumber,
      minSpeed: minSpeed ?? this.minSpeed,
      maxSpeed: maxSpeed ?? this.maxSpeed,
    );
  }
}
