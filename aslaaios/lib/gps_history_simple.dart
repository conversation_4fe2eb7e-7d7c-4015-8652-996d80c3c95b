import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as google_maps;
import 'package:aslaa/models/gps_history.dart';
import 'package:aslaa/service/gps_history_service.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:aslaa/flutter_flow/flutter_flow_theme.dart';
import 'package:aslaa/flutter_flow/internationalization.dart';
import 'dart:async';
import 'dart:ui' as ui;
import 'dart:math' as math;

enum GpsHistoryViewMode { timeline, map }

class GpsHistoryPage extends StatefulWidget {
  const GpsHistoryPage({Key? key}) : super(key: key);

  @override
  _GpsHistoryPageState createState() => _GpsHistoryPageState();
}

class _GpsHistoryPageState extends State<GpsHistoryPage> {
  late GpsHistoryService _gpsHistoryService;

  // Deduplication configuration - adjust these values to control GPS point density
  static const double _minDistanceMeters =
      50.0; // Minimum distance between points (increased from backend's 10m)
  static const int _minTimeSeconds =
      60; // Minimum time between points (increased from backend's 30s)

  GpsHistoryResponse? _gpsHistoryResponse;
  bool _isLoading = false;
  String? _errorMessage;
  DateTime _selectedDate = DateTime.now();
  GpsHistoryViewMode _viewMode = GpsHistoryViewMode.timeline;

  // Map related variables
  final Completer<google_maps.GoogleMapController> _mapController = Completer();
  Set<google_maps.Marker> _markers = {};
  Set<google_maps.Polyline> _polylines = {};
  google_maps.LatLng? _mapCenter;

  // Playback related variables
  bool _isPlaying = false;
  int _currentPlaybackIndex = 0;
  Timer? _playbackTimer;
  double _playbackSpeed = 1.0; // 1x speed
  google_maps.Marker? _currentPositionMarker;

  // Smooth animation variables
  double _interpolationProgress = 0.0;
  int _interpolationSteps = 20; // Number of steps between GPS points

  // Custom marker icons
  google_maps.BitmapDescriptor? _smallDotIcon;
  google_maps.BitmapDescriptor? _startDotIcon;
  google_maps.BitmapDescriptor? _endDotIcon;
  google_maps.BitmapDescriptor? _carIcon;

  @override
  void initState() {
    super.initState();
    _gpsHistoryService = GpsHistoryService();
    _createCustomMarkerIcons();

    // Load today's GPS history by default
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadGpsHistory();
    });
  }

  Future<void> _createCustomMarkerIcons() async {
    // Create small dot icon
    _smallDotIcon = await _createDotIcon(Colors.yellow, 8);

    // Create start dot icon
    _startDotIcon = await _createDotIcon(Colors.green, 12);

    // Create end dot icon
    _endDotIcon = await _createDotIcon(Colors.red, 12);

    // Use existing car icon from assets
    _carIcon = await _createCarIconFromAsset();
  }

  Future<google_maps.BitmapDescriptor> _createDotIcon(
      Color color, double size) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    canvas.drawCircle(Offset(size, size), size, paint);
    canvas.drawCircle(Offset(size, size), size, strokePaint);

    final picture = recorder.endRecording();
    final image = await picture.toImage((size * 2).toInt(), (size * 2).toInt());
    final bytes = await image.toByteData(format: ui.ImageByteFormat.png);

    return google_maps.BitmapDescriptor.bytes(bytes!.buffer.asUint8List());
  }

  Future<google_maps.BitmapDescriptor> _createCarIconFromAsset() async {
    try {
      // Use the existing car icon from assets
      return await google_maps.BitmapDescriptor.asset(
        const ImageConfiguration(size: Size(40, 40)),
        'assets/images/car-image-icon.png',
      );
    } catch (e) {
      // Fallback to a default marker if asset loading fails
      return google_maps.BitmapDescriptor.defaultMarkerWithHue(
        google_maps.BitmapDescriptor.hueBlue,
      );
    }
  }

  Future<void> _loadGpsHistory() async {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final user = appProvider.authClient;

    if (user == null || user.device == null) {
      setState(() {
        _errorMessage = 'No device connected';
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _gpsHistoryService.fetchGpsHistoryForUser(
        user: user,
        fromDate: _selectedDate,
        toDate: _selectedDate,
      );

      setState(() {
        _gpsHistoryResponse = response;
        _isLoading = false;

        if (!response.success) {
          _errorMessage = response.error ?? 'Failed to load GPS history';
        } else {
          _setupMapData();
        }
      });
    } catch (error) {
      setState(() {
        _errorMessage = 'Error loading GPS history: $error';
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(Duration(days: 365)),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _loadGpsHistory();
    }
  }

  @override
  void dispose() {
    _playbackTimer?.cancel();
    super.dispose();
  }

  void _setupMapData() {
    if (_gpsHistoryResponse == null || !_gpsHistoryResponse!.hasValidData)
      return;

    _createMarkers();
    _createPolylines();
    _setMapCenter();
  }

  // Client-side deduplication with increased distance radius
  List<GpsHistoryPoint> _deduplicateGpsPoints(List<GpsHistoryPoint> points) {
    if (points.length <= 1) return points;

    final deduplicatedPoints = <GpsHistoryPoint>[];

    // Always include the first point
    deduplicatedPoints.add(points.first);

    for (int i = 1; i < points.length; i++) {
      final currentPoint = points[i];
      final lastIncludedPoint = deduplicatedPoints.last;

      // Calculate distance between points
      final distance = _calculateDistance(
        lastIncludedPoint.lat,
        lastIncludedPoint.lng,
        currentPoint.lat,
        currentPoint.lng,
      );

      // Calculate time difference
      final timeDiff = currentPoint.createdAt
          .difference(lastIncludedPoint.createdAt)
          .inSeconds
          .abs();

      // Include point if it's far enough or enough time has passed
      if (distance >= _minDistanceMeters || timeDiff >= _minTimeSeconds) {
        deduplicatedPoints.add(currentPoint);
      }
    }

    // Always include the last point if it's not already included
    if (deduplicatedPoints.last.id != points.last.id) {
      deduplicatedPoints.add(points.last);
    }

    return deduplicatedPoints;
  }

  // Calculate distance between two GPS points using Haversine formula
  double _calculateDistance(
      double lat1, double lng1, double lat2, double lng2) {
    const double earthRadius = 6371000; // Earth's radius in meters
    final double dLat = (lat2 - lat1) * (math.pi / 180);
    final double dLng = (lng2 - lng1) * (math.pi / 180);

    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(lat1 * (math.pi / 180)) *
            math.cos(lat2 * (math.pi / 180)) *
            math.sin(dLng / 2) *
            math.sin(dLng / 2);

    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  void _createMarkers() {
    _markers.clear();
    final validPoints = _deduplicateGpsPoints(_gpsHistoryResponse!.validPoints);

    for (int i = 0; i < validPoints.length; i++) {
      final point = validPoints[i];
      final isFirst = i == 0;
      final isLast = i == validPoints.length - 1;

      _markers.add(
        google_maps.Marker(
          markerId: google_maps.MarkerId(point.id),
          position: google_maps.LatLng(point.lat, point.lng),
          icon: _getSmallDotIcon(point.speed, isFirst, isLast),
          infoWindow: google_maps.InfoWindow(
            title: point.formattedTime,
            snippet: '${point.formattedSpeed} • ${point.coordinatesString}',
          ),
        ),
      );
    }
  }

  google_maps.BitmapDescriptor _getSmallDotIcon(
      double speed, bool isFirst, bool isLast) {
    // Use custom small dots for all GPS points
    if (isFirst && _startDotIcon != null) {
      return _startDotIcon!;
    } else if (isLast && _endDotIcon != null) {
      return _endDotIcon!;
    } else if (_smallDotIcon != null) {
      return _smallDotIcon!;
    } else {
      // Fallback to default markers if custom icons aren't ready
      return google_maps.BitmapDescriptor.defaultMarkerWithHue(
          google_maps.BitmapDescriptor.hueYellow);
    }
  }

  void _createPolylines() {
    _polylines.clear();
    final validPoints = _deduplicateGpsPoints(_gpsHistoryResponse!.validPoints);

    if (validPoints.length < 2) return;

    List<google_maps.LatLng> polylineCoordinates = validPoints
        .map((point) => google_maps.LatLng(point.lat, point.lng))
        .toList();

    _polylines.add(
      google_maps.Polyline(
        polylineId: google_maps.PolylineId('gps_route'),
        points: polylineCoordinates,
        color: Colors.blue,
        width: 3,
      ),
    );
  }

  void _setMapCenter() {
    final validPoints = _gpsHistoryResponse!.validPoints;
    if (validPoints.isNotEmpty) {
      final firstPoint = validPoints.first;
      _mapCenter = google_maps.LatLng(firstPoint.lat, firstPoint.lng);
    }
  }

  void _startPlayback() {
    if (_gpsHistoryResponse == null || !_gpsHistoryResponse!.hasValidData)
      return;

    setState(() {
      _isPlaying = true;
      _currentPlaybackIndex = 0;
      _interpolationProgress = 0.0;
    });

    // Use shorter intervals for smoother animation
    _playbackTimer = Timer.periodic(
        Duration(milliseconds: (50 / _playbackSpeed).round()), (timer) {
      _updatePlaybackPosition();
    });
  }

  void _stopPlayback() {
    _playbackTimer?.cancel();
    setState(() {
      _isPlaying = false;
      _currentPlaybackIndex = 0;
      _interpolationProgress = 0.0;
    });
    _removeCurrentPositionMarker();
  }

  void _pausePlayback() {
    _playbackTimer?.cancel();
    setState(() {
      _isPlaying = false;
    });
  }

  void _resumePlayback() {
    final validPoints = _deduplicateGpsPoints(_gpsHistoryResponse!.validPoints);
    if (_currentPlaybackIndex >= validPoints.length) {
      _startPlayback();
      return;
    }

    setState(() {
      _isPlaying = true;
    });

    // Use shorter intervals for smoother animation
    _playbackTimer = Timer.periodic(
        Duration(milliseconds: (50 / _playbackSpeed).round()), (timer) {
      _updatePlaybackPosition();
    });
  }

  void _updatePlaybackPosition() async {
    final validPoints = _deduplicateGpsPoints(_gpsHistoryResponse!.validPoints);

    if (_currentPlaybackIndex >= validPoints.length - 1) {
      _stopPlayback();
      return;
    }

    // Get current and next GPS points for interpolation
    final currentPoint = validPoints[_currentPlaybackIndex];
    final nextPoint = validPoints[_currentPlaybackIndex + 1];

    // Calculate interpolated position
    final interpolatedLat =
        _lerp(currentPoint.lat, nextPoint.lat, _interpolationProgress);
    final interpolatedLng =
        _lerp(currentPoint.lng, nextPoint.lng, _interpolationProgress);
    final interpolatedPosition =
        google_maps.LatLng(interpolatedLat, interpolatedLng);

    // Create interpolated point for display
    final interpolatedGpsPoint = GpsHistoryPoint(
      id: 'interpolated',
      lat: interpolatedLat,
      lng: interpolatedLng,
      speed: _lerp(currentPoint.speed, nextPoint.speed, _interpolationProgress),
      createdAt: currentPoint.createdAt, // Use current point's timestamp
      deviceNumber: currentPoint.deviceNumber,
      address: currentPoint.address,
    );

    // Update current position marker with smooth position
    await _updateCurrentPositionMarker(interpolatedGpsPoint);

    // Smoothly move camera to interpolated position
    final controller = await _mapController.future;
    controller.animateCamera(
      google_maps.CameraUpdate.newLatLng(interpolatedPosition),
    );

    // Update interpolation progress
    _interpolationProgress += 1.0 / _interpolationSteps;

    if (_interpolationProgress >= 1.0) {
      // Move to next GPS point
      _interpolationProgress = 0.0;
      setState(() {
        _currentPlaybackIndex++;
      });
    }
  }

  // Linear interpolation helper function
  double _lerp(double start, double end, double progress) {
    return start + (end - start) * progress;
  }

  Future<void> _updateCurrentPositionMarker(GpsHistoryPoint point) async {
    // Remove previous current position marker
    _removeCurrentPositionMarker();

    // Add new current position marker with custom car icon
    _currentPositionMarker = google_maps.Marker(
      markerId: google_maps.MarkerId('current_position'),
      position: google_maps.LatLng(point.lat, point.lng),
      icon: _carIcon ??
          google_maps.BitmapDescriptor.defaultMarkerWithHue(
              google_maps.BitmapDescriptor.hueBlue),
      infoWindow: google_maps.InfoWindow(
        title: '🚗 Current Position',
        snippet: '${point.formattedTime} • ${point.formattedSpeed}',
      ),
      // Make the current position marker more prominent
      zIndexInt: 1000,
    );

    setState(() {
      _markers.add(_currentPositionMarker!);
    });
  }

  void _removeCurrentPositionMarker() {
    if (_currentPositionMarker != null) {
      setState(() {
        _markers.remove(_currentPositionMarker);
        _currentPositionMarker = null;
      });
    }
  }

  void _changePlaybackSpeed(double speed) {
    setState(() {
      _playbackSpeed = speed;
    });

    if (_isPlaying) {
      _pausePlayback();
      _resumePlayback();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                FlutterFlowTheme.of(context).secondaryBackground,
                FlutterFlowTheme.of(context)
                    .secondaryBackground
                    .withValues(alpha: 0.95),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: FlutterFlowTheme.of(context)
                    .primaryText
                    .withValues(alpha: 0.1),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: AppBar(
            title: Text(
              FFLocalizations.of(context).getText('6g39sgd4' /* GPS HISTORY */),
              style: FlutterFlowTheme.of(context).title1.override(
                    fontFamily: 'Readex Pro',
                    color: FlutterFlowTheme.of(context).primaryText,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
            ),
            backgroundColor: Colors.transparent,
            foregroundColor: FlutterFlowTheme.of(context).primaryText,
            iconTheme: IconThemeData(
              color: FlutterFlowTheme.of(context).primaryText,
              size: 24,
            ),
            elevation: 0,
            centerTitle: true,
            systemOverlayStyle: FlutterFlowTheme.of(context)
                        .secondaryBackground
                        .computeLuminance() >
                    0.5
                ? SystemUiOverlayStyle.dark
                : SystemUiOverlayStyle.light,
          ),
        ),
      ),
      body: Column(
        children: [
          _buildDateSelector(),
          _buildViewModeToggle(),
          if (_gpsHistoryResponse != null && _gpsHistoryResponse!.success)
            _buildStatsCard(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelector() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
            color: FlutterFlowTheme.of(context)
                .secondaryText
                .withValues(alpha: 0.2),
            width: 1),
        boxShadow: [
          BoxShadow(
            color: FlutterFlowTheme.of(context)
                .primaryText
                .withValues(alpha: 0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(Icons.calendar_today,
              color: FlutterFlowTheme.of(context).primaryColor, size: 20),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
              style: FlutterFlowTheme.of(context).bodyText1.override(
                    fontFamily: 'Readex Pro',
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
          IconButton(
            onPressed: _selectDate,
            icon: Icon(Icons.edit,
                color: FlutterFlowTheme.of(context).primaryColor, size: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildViewModeToggle() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
            color: FlutterFlowTheme.of(context)
                .secondaryText
                .withValues(alpha: 0.2),
            width: 1),
        boxShadow: [
          BoxShadow(
            color: FlutterFlowTheme.of(context)
                .primaryText
                .withValues(alpha: 0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () =>
                  setState(() => _viewMode = GpsHistoryViewMode.timeline),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _viewMode == GpsHistoryViewMode.timeline
                      ? FlutterFlowTheme.of(context).secondaryColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: _viewMode == GpsHistoryViewMode.timeline
                      ? [
                          BoxShadow(
                            color: FlutterFlowTheme.of(context)
                                .secondaryColor
                                .withValues(alpha: 0.3),
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.list,
                      color: _viewMode == GpsHistoryViewMode.timeline
                          ? Colors.white
                          : FlutterFlowTheme.of(context).secondaryText,
                      size: 16,
                    ),
                    SizedBox(width: 8),
                    Text(
                      FFLocalizations.of(context)
                          .getText('gps_timeline' /* Timeline */),
                      style: FlutterFlowTheme.of(context).bodyText1.override(
                            fontFamily: 'Readex Pro',
                            color: _viewMode == GpsHistoryViewMode.timeline
                                ? Colors.white
                                : FlutterFlowTheme.of(context).secondaryText,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _viewMode = GpsHistoryViewMode.map),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _viewMode == GpsHistoryViewMode.map
                      ? FlutterFlowTheme.of(context).secondaryColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: _viewMode == GpsHistoryViewMode.map
                      ? [
                          BoxShadow(
                            color: FlutterFlowTheme.of(context)
                                .secondaryColor
                                .withValues(alpha: 0.3),
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.map,
                      color: _viewMode == GpsHistoryViewMode.map
                          ? Colors.white
                          : FlutterFlowTheme.of(context).secondaryText,
                      size: 16,
                    ),
                    SizedBox(width: 8),
                    Text(
                      FFLocalizations.of(context).getText('gps_map' /* Map */),
                      style: FlutterFlowTheme.of(context).bodyText1.override(
                            fontFamily: 'Readex Pro',
                            color: _viewMode == GpsHistoryViewMode.map
                                ? Colors.white
                                : FlutterFlowTheme.of(context).secondaryText,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard() {
    if (_gpsHistoryResponse == null || !_gpsHistoryResponse!.success) {
      return SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
            color: FlutterFlowTheme.of(context)
                .secondaryText
                .withValues(alpha: 0.2),
            width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            FFLocalizations.of(context)
                .getText('gps_statistics' /* GPS Statistics */),
            style: FlutterFlowTheme.of(context).title3.override(
                  fontFamily: 'Readex Pro',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
          ),
          SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildStatItem(
                  FFLocalizations.of(context)
                      .getText('total_points' /* Total Points */),
                  _gpsHistoryResponse!.totalFound.toString()),
              _buildStatItem(
                  FFLocalizations.of(context)
                      .getText('valid_points' /* Valid Points */),
                  _gpsHistoryResponse!.validCoordinates.toString()),
              _buildStatItem(
                  FFLocalizations.of(context)
                      .getText('deduplicated' /* Deduplicated */),
                  _gpsHistoryResponse!.deduplicatedPoints.toString()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: FlutterFlowTheme.of(context).title2.override(
                fontFamily: 'Readex Pro',
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: FlutterFlowTheme.of(context).primaryColor,
              ),
        ),
        Text(
          label,
          style: FlutterFlowTheme.of(context).bodyText2.override(
                fontFamily: 'Readex Pro',
                fontSize: 12,
                color: FlutterFlowTheme.of(context).secondaryText,
              ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
            color: FlutterFlowTheme.of(context).primaryColor),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline,
                size: 64, color: FlutterFlowTheme.of(context).secondaryText),
            SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: FlutterFlowTheme.of(context).bodyText1.override(
                    fontFamily: 'Readex Pro',
                    color: FlutterFlowTheme.of(context).secondaryText,
                  ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadGpsHistory,
              child: Text(
                  FFLocalizations.of(context).getText('retry' /* Retry */)),
            ),
          ],
        ),
      );
    }

    if (_gpsHistoryResponse == null || !_gpsHistoryResponse!.hasValidData) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.location_off,
                size: 64, color: FlutterFlowTheme.of(context).secondaryText),
            SizedBox(height: 16),
            Text(
              FFLocalizations.of(context).getText(
                  'no_gps_data' /* No GPS data found for selected date */),
              style: FlutterFlowTheme.of(context).bodyText1.override(
                    fontFamily: 'Readex Pro',
                    color: FlutterFlowTheme.of(context).secondaryText,
                  ),
            ),
          ],
        ),
      );
    }

    // Show the appropriate view based on selected mode
    switch (_viewMode) {
      case GpsHistoryViewMode.timeline:
        return _buildTimelineView();
      case GpsHistoryViewMode.map:
        return _buildMapView();
    }
  }

  Widget _buildTimelineView() {
    final validPoints = _deduplicateGpsPoints(_gpsHistoryResponse!.validPoints);

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: validPoints.length,
      itemBuilder: (context, index) {
        final point = validPoints[index];
        return Container(
          margin: EdgeInsets.only(bottom: 12),
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: FlutterFlowTheme.of(context).secondaryBackground,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
                color: FlutterFlowTheme.of(context)
                    .secondaryText
                    .withValues(alpha: 0.2),
                width: 1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(Icons.access_time,
                          size: 16,
                          color: FlutterFlowTheme.of(context).primaryColor),
                      SizedBox(width: 4),
                      Text(
                        point.formattedTime,
                        style: FlutterFlowTheme.of(context).bodyText1.override(
                              fontFamily: 'Readex Pro',
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ],
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getSpeedColor(point.speed),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      point.formattedSpeed,
                      style: FlutterFlowTheme.of(context).bodyText2.override(
                            fontFamily: 'Readex Pro',
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.location_on,
                      size: 16,
                      color: FlutterFlowTheme.of(context).secondaryText),
                  SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      point.coordinatesString,
                      style: FlutterFlowTheme.of(context).bodyText2.override(
                            fontFamily: 'Readex Pro',
                            color: FlutterFlowTheme.of(context).secondaryText,
                            fontSize: 12,
                          ),
                    ),
                  ),
                ],
              ),
              if (point.address != null && point.address!.isNotEmpty) ...[
                SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.place,
                        size: 16,
                        color: FlutterFlowTheme.of(context).secondaryText),
                    SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        point.address!,
                        style: FlutterFlowTheme.of(context).bodyText2.override(
                              fontFamily: 'Readex Pro',
                              color: FlutterFlowTheme.of(context).secondaryText,
                              fontSize: 12,
                            ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildMapView() {
    final validPoints =
        _deduplicateGpsPoints(_gpsHistoryResponse?.validPoints ?? []);

    if (validPoints.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.map,
                size: 64, color: FlutterFlowTheme.of(context).secondaryText),
            SizedBox(height: 16),
            Text(
              FFLocalizations.of(context).getText(
                  'no_gps_map_data' /* No GPS data to display on map */),
              style: FlutterFlowTheme.of(context).bodyText1.override(
                    fontFamily: 'Readex Pro',
                    color: FlutterFlowTheme.of(context).secondaryText,
                  ),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        google_maps.GoogleMap(
          onMapCreated: (google_maps.GoogleMapController controller) {
            _mapController.complete(controller);
          },
          initialCameraPosition: google_maps.CameraPosition(
            target: _mapCenter ??
                google_maps.LatLng(
                    validPoints.first.lat, validPoints.first.lng),
            zoom: 15,
          ),
          markers: _markers,
          polylines: _polylines,
          mapType: google_maps.MapType.normal,
          myLocationEnabled: false,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: true,
          compassEnabled: true,
          mapToolbarEnabled: false,
        ),
        // Playback Controls
        Positioned(
          bottom: 16,
          left: 16,
          right: 16,
          child: _buildPlaybackControls(),
        ),
        // Legend
        Positioned(
          top: 16,
          right: 16,
          child: Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).secondaryBackground,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: FlutterFlowTheme.of(context)
                      .primaryText
                      .withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  FFLocalizations.of(context).getText('legend' /* Legend */),
                  style: FlutterFlowTheme.of(context).bodyText2.override(
                        fontFamily: 'Readex Pro',
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                ),
                SizedBox(height: 8),
                _buildLegendItem(
                    Colors.blue,
                    FFLocalizations.of(context)
                        .getText('current_position' /* 🚗 Current Position */)),
                _buildLegendItem(
                    Colors.green,
                    FFLocalizations.of(context)
                        .getText('start_point' /* Start Point */)),
                _buildLegendItem(
                    Colors.red,
                    FFLocalizations.of(context)
                        .getText('end_point' /* End Point */)),
                _buildLegendItem(
                    Colors.yellow,
                    FFLocalizations.of(context)
                        .getText('gps_points' /* GPS Points */)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(Color color, String label) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 6),
          Text(
            label,
            style: FlutterFlowTheme.of(context).bodyText2.override(
                  fontFamily: 'Readex Pro',
                  fontSize: 10,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaybackControls() {
    final validPoints =
        _deduplicateGpsPoints(_gpsHistoryResponse?.validPoints ?? []);
    final totalPoints = validPoints.length;
    final progress =
        totalPoints > 0 ? _currentPlaybackIndex / totalPoints : 0.0;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color:
                FlutterFlowTheme.of(context).primaryText.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Progress bar
          Row(
            children: [
              Text(
                '${_currentPlaybackIndex}',
                style: FlutterFlowTheme.of(context).bodyText2.override(
                      fontFamily: 'Readex Pro',
                      fontSize: 12,
                      color: FlutterFlowTheme.of(context).secondaryText,
                    ),
              ),
              Expanded(
                child: Slider(
                  value: progress.clamp(0.0, 1.0),
                  onChanged: (value) {
                    setState(() {
                      _currentPlaybackIndex = (value * totalPoints).round();
                    });
                    if (_currentPlaybackIndex < totalPoints) {
                      final point = validPoints[_currentPlaybackIndex];
                      _updateCurrentPositionMarker(point);
                    }
                  },
                  activeColor: FlutterFlowTheme.of(context).primaryColor,
                  inactiveColor: FlutterFlowTheme.of(context)
                      .secondaryText
                      .withValues(alpha: 0.3),
                ),
              ),
              Text(
                '$totalPoints',
                style: FlutterFlowTheme.of(context).bodyText2.override(
                      fontFamily: 'Readex Pro',
                      fontSize: 12,
                      color: FlutterFlowTheme.of(context).secondaryText,
                    ),
              ),
            ],
          ),
          SizedBox(height: 8),
          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Speed control
              PopupMenuButton<double>(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context)
                        .secondaryText
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${_playbackSpeed}x',
                    style: FlutterFlowTheme.of(context).bodyText2.override(
                          fontFamily: 'Readex Pro',
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                itemBuilder: (context) => [
                  PopupMenuItem(value: 0.5, child: Text('0.5x')),
                  PopupMenuItem(value: 1.0, child: Text('1x')),
                  PopupMenuItem(value: 2.0, child: Text('2x')),
                  PopupMenuItem(value: 5.0, child: Text('5x')),
                  PopupMenuItem(value: 10.0, child: Text('10x')),
                ],
                onSelected: _changePlaybackSpeed,
              ),
              // Stop button
              IconButton(
                onPressed: _stopPlayback,
                icon: Icon(Icons.stop,
                    color: FlutterFlowTheme.of(context).secondaryText),
                style: IconButton.styleFrom(
                  backgroundColor: FlutterFlowTheme.of(context)
                      .secondaryText
                      .withValues(alpha: 0.1),
                  shape: CircleBorder(),
                ),
              ),
              // Play/Pause button
              IconButton(
                onPressed: _isPlaying ? _pausePlayback : _resumePlayback,
                icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow,
                    color: FlutterFlowTheme.of(context).primaryColor),
                style: IconButton.styleFrom(
                  backgroundColor: FlutterFlowTheme.of(context)
                      .primaryColor
                      .withValues(alpha: 0.1),
                  shape: CircleBorder(),
                ),
              ),
              // Restart button
              IconButton(
                onPressed: _startPlayback,
                icon: Icon(Icons.replay,
                    color: FlutterFlowTheme.of(context).secondaryColor),
                style: IconButton.styleFrom(
                  backgroundColor: FlutterFlowTheme.of(context)
                      .secondaryText
                      .withValues(alpha: 0.1),
                  shape: CircleBorder(),
                ),
              ),
            ],
          ),
          if (_currentPlaybackIndex < totalPoints && totalPoints > 0) ...[
            SizedBox(height: 8),
            Text(
              '${FFLocalizations.of(context).getText('current' /* Current */)}: ${validPoints[_currentPlaybackIndex.clamp(0, totalPoints - 1)].formattedTime} • ${validPoints[_currentPlaybackIndex.clamp(0, totalPoints - 1)].formattedSpeed}',
              style: FlutterFlowTheme.of(context).bodyText2.override(
                    fontFamily: 'Readex Pro',
                    fontSize: 12,
                    color: FlutterFlowTheme.of(context).secondaryText,
                  ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getSpeedColor(double speed) {
    if (speed == 0) return Colors.grey;
    if (speed < 30) return Colors.green;
    if (speed < 60) return Colors.orange;
    return Colors.red;
  }
}
