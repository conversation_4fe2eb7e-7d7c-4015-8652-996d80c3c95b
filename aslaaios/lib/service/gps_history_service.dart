import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:aslaa/models/gps_history.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/constant.dart';

class GpsHistoryService {
  final String apiHost;

  GpsHistoryService({String? apiHost}) : apiHost = apiHost ?? API_HOST;

  /// Fetch GPS history for a specific date range
  Future<GpsHistoryResponse> fetchGpsHistoryByDate({
    required String deviceNumber,
    required DateTime fromDate,
    required DateTime toDate,
    String? token,
  }) async {
    try {
      final url = Uri.parse('$apiHost/api/log/gps-get-by-date');
      
      // Format dates for the API
      final fromDateStr = fromDate.toIso8601String().split('T')[0];
      final toDateStr = toDate.toIso8601String().split('T')[0];
      
      final headers = {
        'Content-Type': 'application/json; charset=UTF-8',
      };
      
      // Add authorization header if token is provided
      if (token != null && token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
      }

      final body = jsonEncode({
        'deviceNumber': deviceNumber,
        'from': fromDateStr,
        'to': toDateStr,
      });

      print('Fetching GPS history for device $deviceNumber from $fromDateStr to $toDateStr');

      final response = await http.post(
        url,
        headers: headers,
        body: body,
      );

      print('GPS History API Response Status: ${response.statusCode}');
      print('GPS History API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return GpsHistoryResponse.fromJson(responseData);
      } else {
        return GpsHistoryResponse(
          success: false,
          logs: [],
          totalFound: 0,
          validCoordinates: 0,
          deduplicatedPoints: 0,
          duplicatesRemoved: 0,
          source: 'error',
          error: 'HTTP ${response.statusCode}: ${response.reasonPhrase}',
        );
      }
    } catch (error) {
      print('Error fetching GPS history: $error');
      return GpsHistoryResponse(
        success: false,
        logs: [],
        totalFound: 0,
        validCoordinates: 0,
        deduplicatedPoints: 0,
        duplicatesRemoved: 0,
        source: 'error',
        error: error.toString(),
      );
    }
  }

  /// Fetch GPS history for today
  Future<GpsHistoryResponse> fetchTodayGpsHistory({
    required String deviceNumber,
    String? token,
  }) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    return fetchGpsHistoryByDate(
      deviceNumber: deviceNumber,
      fromDate: today,
      toDate: today,
      token: token,
    );
  }

  /// Fetch GPS history for yesterday
  Future<GpsHistoryResponse> fetchYesterdayGpsHistory({
    required String deviceNumber,
    String? token,
  }) async {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    
    return fetchGpsHistoryByDate(
      deviceNumber: deviceNumber,
      fromDate: yesterday,
      toDate: yesterday,
      token: token,
    );
  }

  /// Fetch GPS history for the last week
  Future<GpsHistoryResponse> fetchWeekGpsHistory({
    required String deviceNumber,
    String? token,
  }) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekAgo = today.subtract(Duration(days: 7));
    
    return fetchGpsHistoryByDate(
      deviceNumber: deviceNumber,
      fromDate: weekAgo,
      toDate: today,
      token: token,
    );
  }

  /// Fetch GPS history using a filter
  Future<GpsHistoryResponse> fetchGpsHistoryWithFilter({
    required GpsHistoryFilter filter,
    String? token,
  }) async {
    if (filter.deviceNumber == null) {
      return GpsHistoryResponse(
        success: false,
        logs: [],
        totalFound: 0,
        validCoordinates: 0,
        deduplicatedPoints: 0,
        duplicatesRemoved: 0,
        source: 'error',
        error: 'Device number is required',
      );
    }

    final fromDate = filter.fromDate ?? DateTime.now();
    final toDate = filter.toDate ?? DateTime.now();

    return fetchGpsHistoryByDate(
      deviceNumber: filter.deviceNumber!,
      fromDate: fromDate,
      toDate: toDate,
      token: token,
    );
  }

  /// Helper method to get GPS history with user authentication
  Future<GpsHistoryResponse> fetchGpsHistoryForUser({
    required User user,
    required DateTime fromDate,
    required DateTime toDate,
  }) async {
    if (user.device == null) {
      return GpsHistoryResponse(
        success: false,
        logs: [],
        totalFound: 0,
        validCoordinates: 0,
        deduplicatedPoints: 0,
        duplicatesRemoved: 0,
        source: 'error',
        error: 'No device connected',
      );
    }

    return fetchGpsHistoryByDate(
      deviceNumber: user.device!.deviceNumber,
      fromDate: fromDate,
      toDate: toDate,
      token: user.token,
    );
  }
}
