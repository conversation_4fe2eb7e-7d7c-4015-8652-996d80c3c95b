import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:aslaa/flutter_flow/flutter_flow_theme.dart';
import 'dart:async';
import 'package:flutter/services.dart'; // Import for HapticFeedback

class ScrollableMapButton extends StatefulWidget {
  final bool isRecording;
  final Future<void> Function() onPressed;
  final VoidCallback? onHistoryPressed;

  ScrollableMapButton({
    Key? key,
    required this.isRecording,
    required this.onPressed,
    this.onHistoryPressed,
  }) : super(key: key);

  @override
  _ScrollableMapButtonState createState() => _ScrollableMapButtonState();
}

class _ScrollableMapButtonState extends State<ScrollableMapButton>
    with TickerProviderStateMixin {
  Timer? _animationTimer;
  bool _showAnimation = false;

  // Icon state management
  IconData? currentIcon;
  final IconData defaultIcon = FontAwesomeIcons.locationDot; // Map icon
  final IconData historyIcon = FontAwesomeIcons.clockRotateLeft; // History icon
  bool isHistoryMode = false;

  @override
  void initState() {
    super.initState();
    currentIcon = defaultIcon;
    if (widget.isRecording) {
      _startAnimationTimer();
    }
  }

  void _startAnimationTimer() {
    _showAnimation = true;
    _animationTimer = Timer(Duration(seconds: 3), () {
      setState(() {
        _showAnimation = false;
      });
    });
  }

  @override
  void dispose() {
    _animationTimer?.cancel();
    super.dispose();
  }

  void _onScroll(DragUpdateDetails details) {
    setState(() {
      if (details.primaryDelta! > 0) {
        // Scrolled down - show history icon
        currentIcon = historyIcon;
        isHistoryMode = true;
      } else if (details.primaryDelta! < 0) {
        // Scrolled up - show map icon
        currentIcon = defaultIcon;
        isHistoryMode = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onVerticalDragUpdate: _onScroll,
      onTap: widget.isRecording
          ? null
          : () async {
              HapticFeedback.lightImpact(); // Add HapticFeedback
              if (isHistoryMode && widget.onHistoryPressed != null) {
                widget.onHistoryPressed!();
              } else {
                await widget.onPressed();
              }
            },
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: widget.isRecording
              ? Colors.red.withValues(alpha: 0.5)
              : Color(0x6939D2C0),
          borderRadius: BorderRadius.circular(40),
          border: Border.all(
            color: FlutterFlowTheme.of(context).secondaryColor,
            width: 1,
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            _showAnimation
                ? CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  )
                : FaIcon(
                    currentIcon ?? defaultIcon,
                    color: FlutterFlowTheme.of(context).primaryText,
                    size: 30,
                  ),
          ],
        ),
      ),
    );
  }
}
