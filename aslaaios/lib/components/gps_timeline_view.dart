import 'package:flutter/material.dart';

import 'package:aslaa/flutter_flow/flutter_flow_theme.dart';
import 'package:aslaa/models/gps_history.dart';

class GpsTimelineView extends StatefulWidget {
  final List<GpsHistoryPoint> gpsPoints;

  const GpsTimelineView({
    Key? key,
    required this.gpsPoints,
  }) : super(key: key);

  @override
  _GpsTimelineViewState createState() => _GpsTimelineViewState();
}

class _GpsTimelineViewState extends State<GpsTimelineView> {
  GpsHistoryPoint? _selectedPoint;

  @override
  Widget build(BuildContext context) {
    if (widget.gpsPoints.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.timeline,
              size: 64,
              color: FlutterFlowTheme.of(context).secondaryText,
            ),
            SizedBox(height: 16),
            Text(
              'No GPS timeline data available',
              style: FlutterFlowTheme.of(context).bodyText1.override(
                    fontFamily: 'PorscheNumber',
                    color: FlutterFlowTheme.of(context).secondaryText,
                  ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: widget.gpsPoints.length,
      itemBuilder: (context, index) {
        final point = widget.gpsPoints[index];
        final isFirst = index == 0;
        final isLast = index == widget.gpsPoints.length - 1;
        final isSelected = _selectedPoint?.id == point.id;

        return _buildTimelineItem(
          point: point,
          isFirst: isFirst,
          isLast: isLast,
          isSelected: isSelected,
          onTap: () {
            setState(() {
              _selectedPoint = isSelected ? null : point;
            });
          },
        );
      },
    );
  }

  Widget _buildTimelineItem({
    required GpsHistoryPoint point,
    required bool isFirst,
    required bool isLast,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Timeline line and dot
          SizedBox(
            width: 40,
            child: Column(
              children: [
                // Top line (hidden for first item)
                if (!isFirst)
                  Container(
                    width: 2,
                    height: 20,
                    color: FlutterFlowTheme.of(context).alternate,
                  ),
                // Timeline dot
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getSpeedColor(point.speed),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: FlutterFlowTheme.of(context).primaryBackground,
                      width: 2,
                    ),
                  ),
                ),
                // Bottom line (hidden for last item)
                if (!isLast)
                  Expanded(
                    child: Container(
                      width: 2,
                      color: FlutterFlowTheme.of(context).alternate,
                    ),
                  ),
              ],
            ),
          ),
          // Content
          Expanded(
            child: GestureDetector(
              onTap: onTap,
              child: Container(
                margin: EdgeInsets.only(left: 12, bottom: 16),
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? FlutterFlowTheme.of(context)
                          .primaryColor
                          .withValues(alpha: 0.1)
                      : FlutterFlowTheme.of(context).secondaryBackground,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? FlutterFlowTheme.of(context).primaryColor
                        : FlutterFlowTheme.of(context).alternate,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Time and speed row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 16,
                              color: FlutterFlowTheme.of(context).primaryColor,
                            ),
                            SizedBox(width: 4),
                            Text(
                              point.formattedTime,
                              style: FlutterFlowTheme.of(context)
                                  .bodyText1
                                  .override(
                                    fontFamily: 'PorscheNumber',
                                    fontWeight: FontWeight.bold,
                                    color: FlutterFlowTheme.of(context)
                                        .primaryText,
                                  ),
                            ),
                          ],
                        ),
                        Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: _getSpeedColor(point.speed),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            point.formattedSpeed,
                            style:
                                FlutterFlowTheme.of(context).bodyText2.override(
                                      fontFamily: 'PorscheNumber',
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    // Coordinates
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: FlutterFlowTheme.of(context).secondaryText,
                        ),
                        SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            point.coordinatesString,
                            style:
                                FlutterFlowTheme.of(context).bodyText2.override(
                                      fontFamily: 'PorscheNumber',
                                      color: FlutterFlowTheme.of(context)
                                          .secondaryText,
                                    ),
                          ),
                        ),
                      ],
                    ),
                    // Address (if available)
                    if (point.address != null && point.address!.isNotEmpty) ...[
                      SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.place,
                            size: 16,
                            color: FlutterFlowTheme.of(context).secondaryText,
                          ),
                          SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              point.address!,
                              style: FlutterFlowTheme.of(context)
                                  .bodyText2
                                  .override(
                                    fontFamily: 'PorscheNumber',
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryText,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ],
                    // Expanded details when selected
                    if (isSelected) ...[
                      SizedBox(height: 12),
                      Divider(color: FlutterFlowTheme.of(context).alternate),
                      SizedBox(height: 8),
                      _buildDetailRow(
                          'Full Date/Time', point.formattedDateTime),
                      _buildDetailRow('Latitude', point.lat.toStringAsFixed(6)),
                      _buildDetailRow(
                          'Longitude', point.lng.toStringAsFixed(6)),
                      _buildDetailRow('Speed', point.formattedSpeed),
                      if (point.payload != null && point.payload!.isNotEmpty)
                        _buildDetailRow(
                            'Additional Data', point.payload.toString()),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: FlutterFlowTheme.of(context).bodyText2.override(
                    fontFamily: 'PorscheNumber',
                    fontWeight: FontWeight.bold,
                    color: FlutterFlowTheme.of(context).secondaryText,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: FlutterFlowTheme.of(context).bodyText2.override(
                    fontFamily: 'PorscheNumber',
                    color: FlutterFlowTheme.of(context).primaryText,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getSpeedColor(double speed) {
    if (speed == 0) {
      return Colors.grey;
    } else if (speed < 30) {
      return Colors.green;
    } else if (speed < 60) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
