import 'package:aslaa/mqtt/mqtt_websocket.dart';
import 'package:flutter/material.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/flutter_flow/flutter_flow_theme.dart';
import 'package:aslaa/flutter_flow/internationalization.dart';
import 'package:aslaa/models/device.dart';
import 'package:flutter/services.dart'; // Import for HapticFeedback
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:aslaa/constant.dart'; // Import for API_HOST

class ScrollableIconButton extends StatefulWidget {
  final User user;
  final DeviceStatus deviceStatus;
  final Future<bool> Function(User user, String command) sendDeviceCommand;
  final MqttHandler? mqtthandler; // Nullable

  ScrollableIconButton({
    Key? key,
    required this.user,
    required this.sendDeviceCommand,
    required this.deviceStatus,
    this.mqtthandler,
  }) : super(key: key);

  @override
  _ScrollableIconButtonState createState() => _ScrollableIconButtonState();
}

class _ScrollableIconButtonState extends State<ScrollableIconButton>
    with TickerProviderStateMixin {
  AnimationController? _controller;
  late AnimationController _successAnimationController;

  int _duration = 2; // Duration in seconds for holding the button
  IconData? currentIcon;
  final IconData defaultIcon = Icons.power_settings_new;
  final IconData upIcon = Icons.flip_camera_android;
  final IconData downIcon = Icons.alarm;
  bool isPressed = false;
  bool showSuccess = false;

  // List to store scheduled commands
  List<Map<String, dynamic>> scheduledCommands = [];

  @override
  void initState() {
    super.initState();
    currentIcon = defaultIcon;

    // Initialize the main animation controller
    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: _duration),
    );

    // Initialize the success animation controller
    _successAnimationController = AnimationController(
      vsync: this,
      duration:
          Duration(milliseconds: 500), // Short duration for success animation
    );

    // Add a status listener to the main controller
    _controller!.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // Only execute the command if the animation is fully completed
        _executeCommandAndReset();
      }
    });
  }

  void _executeCommandAndReset() {
    if (currentIcon == downIcon) {
      _showTimePicker();
    } else {
      String command = _constructCommand();

      _sendCommand(command).then((_) {
        // After sending the command, trigger the success animation
        setState(() => showSuccess = true);
        _successAnimationController.forward().then((_) {
          Future.delayed(Duration(seconds: 1), () {
            setState(() => showSuccess = false);
            _successAnimationController.reset();
          });
        });
      }).catchError((error) {
        print('Error sending command: $error');
      });
    }
  }

  String _constructCommand() {
    if (currentIcon == upIcon) {
      return 'as';
    } else if (currentIcon == downIcon) {
      // This won't be used anymore since we're showing time picker instead
      // but keeping it for backward compatibility
      return 'mirror';
    } else {
      String? uix = widget.user.device?.uix;
      if (uix != null) {
        if (uix.toLowerCase().contains('chip')) {
          return widget.deviceStatus.sta == 1 ? 'off1' : 'on1';
        } else if (uix.toLowerCase().contains('car2')) {
          return widget.deviceStatus.sta == 1 ? 'unt' : 'asa';
        }
        return widget.deviceStatus.sta == 1 ? 'untar' : 'as';
      }
      return 'defaultCommand'; // Fallback command
    }
  }

  Future<void> _sendCommand(String command) async {
    print('Constructed command: $command');

    // Send the command via the provided function
    await widget.sendDeviceCommand(widget.user, command);

    setState(() {
      currentIcon = defaultIcon; // Reset the icon after command execution
    });

    _controller!.value = 0.0; // Reset the animation controller
  }

  void _onButtonPressed() {
    HapticFeedback.lightImpact(); // Add HapticFeedback
    print('Button Pressed');
    setState(() => isPressed = true);
    _controller!.forward(from: 0);
    print('Animation Started: ${_controller!.isAnimating}');
  }

  void _onButtonReleased() {
    print('Button Released');
    if (_controller!.isAnimating) {
      _controller!.stop();
      print('Animation Stopped: ${_controller!.isAnimating}');
      setState(() {
        if (_controller!.value < 1.0) {
          print('Command Cancelled');
          currentIcon = defaultIcon;
          _controller!.value = 0.0;
        }
      });
    }
  }

  void _onScroll(DragUpdateDetails details) {
    setState(() {
      if (details.primaryDelta! > 0) {
        currentIcon = downIcon; // Scrolled down
      } else if (details.primaryDelta! < 0) {
        currentIcon = upIcon; // Scrolled up
      }
    });
  }

  Future<void> _showTimePicker() async {
    // Reset animation controller
    _controller!.value = 0.0;

    // Show success animation briefly
    setState(() => showSuccess = true);
    await _successAnimationController.forward();

    // Reset icon after showing success animation
    setState(() {
      showSuccess = false;
      currentIcon = defaultIcon;
    });
    _successAnimationController.reset();

    // Fetch existing scheduled commands
    await _fetchScheduledCommands();

    // Show dialog with options
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Scheduled Commands',
            style: TextStyle(fontFamily: 'PorscheNumber'),
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Button to add new scheduled command - Made more prominent
                Container(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _showNewScheduleDialog();
                    },
                    icon: Icon(
                      Icons.add_alarm,
                      color: Colors.white,
                    ),
                    label: Text(
                      'Add New Schedule',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          FlutterFlowTheme.of(context).secondaryColor,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 3,
                    ),
                  ),
                ),
                SizedBox(height: 16),
                // List of existing scheduled commands
                Flexible(
                  child: scheduledCommands.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.schedule,
                                size: 48,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 8),
                              Text(
                                'No scheduled commands',
                                style: TextStyle(
                                  fontFamily: 'PorscheNumber',
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          itemCount: scheduledCommands.length,
                          itemBuilder: (context, index) {
                            final command = scheduledCommands[index];
                            final scheduledTime =
                                DateTime.parse(command['scheduledTime']);
                            final status = command['status'] ?? 'Unknown';
                            final result = command['executionResult'] ?? '';

                            return ListTile(
                              title: Row(
                                children: [
                                  Text(
                                    'Command: ${_getCommandName(command['payload'])}',
                                    style:
                                        TextStyle(fontFamily: 'PorscheNumber'),
                                  ),
                                  SizedBox(width: 8),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: _getStatusColor(status),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Text(
                                      status,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Time: ${_formatDateTime(scheduledTime)}',
                                    style:
                                        TextStyle(fontFamily: 'PorscheNumber'),
                                  ),
                                  if (result.isNotEmpty)
                                    Text(
                                      'Result: $result',
                                      style: TextStyle(
                                        fontFamily: 'PorscheNumber',
                                        fontStyle: FontStyle.italic,
                                        fontSize: 12,
                                      ),
                                    ),
                                ],
                              ),
                              trailing: IconButton(
                                icon: Icon(Icons.delete),
                                onPressed: () {
                                  _deleteScheduledCommand(command['_id']);
                                  Navigator.pop(context);
                                },
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Close'),
            ),
          ],
        );
      },
    );
  }

  // Format DateTime for display with correct timezone
  String _formatDateTime(DateTime dateTime) {
    // Convert to local time
    final localDateTime = dateTime.toLocal();
    return '${localDateTime.year}-${localDateTime.month.toString().padLeft(2, '0')}-${localDateTime.day.toString().padLeft(2, '0')} ${localDateTime.hour.toString().padLeft(2, '0')}:${localDateTime.minute.toString().padLeft(2, '0')}';
  }

  // Extract command name from payload
  String _getCommandName(String payload) {
    try {
      final Map<String, dynamic> data = json.decode(payload);
      return data['command'] ?? 'Unknown';
    } catch (e) {
      return 'Unknown';
    }
  }

  // Show dialog to create a new scheduled command with integrated date/time picker
  Future<void> _showNewScheduleDialog() async {
    String selectedCommand = 'as'; // Default command is 'as' (start)
    DateTime selectedDateTime =
        DateTime.now().add(Duration(hours: 1)); // Default to 1 hour from now
    bool showDateTimePicker =
        true; // Show date/time picker immediately since we have a default command

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                FFLocalizations.of(context).getText('schedule_command'),
                style: TextStyle(
                  fontFamily: 'PorscheNumber',
                  color: FlutterFlowTheme.of(context).primaryText,
                ),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Command Selection Section
                    Text(
                      FFLocalizations.of(context).getText('select_command'),
                      style: TextStyle(
                        fontFamily: 'PorscheNumber',
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: FlutterFlowTheme.of(context).primaryText,
                      ),
                    ),
                    SizedBox(height: 8),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                            color: FlutterFlowTheme.of(context).lineColor),
                        borderRadius: BorderRadius.circular(8),
                        color: FlutterFlowTheme.of(context).secondaryBackground,
                      ),
                      child: Column(
                        children: [
                          ListTile(
                            title: Text(
                              FFLocalizations.of(context)
                                  .getText('start_engine'),
                              style: TextStyle(
                                fontFamily: 'PorscheNumber',
                                color: FlutterFlowTheme.of(context).primaryText,
                              ),
                            ),
                            leading: Radio<String>(
                              value: 'as',
                              groupValue: selectedCommand,
                              onChanged: (String? value) {
                                if (value != null) {
                                  setState(() {
                                    selectedCommand = value;
                                    showDateTimePicker =
                                        true; // Show date picker immediately
                                  });
                                }
                              },
                              activeColor:
                                  FlutterFlowTheme.of(context).secondaryColor,
                            ),
                          ),
                          Divider(
                            height: 1,
                            color: FlutterFlowTheme.of(context).lineColor,
                          ),
                          ListTile(
                            title: Text(
                              FFLocalizations.of(context)
                                  .getText('stop_engine'),
                              style: TextStyle(
                                fontFamily: 'PorscheNumber',
                                color: FlutterFlowTheme.of(context).primaryText,
                              ),
                            ),
                            leading: Radio<String>(
                              value: 'untar',
                              groupValue: selectedCommand,
                              onChanged: (String? value) {
                                if (value != null) {
                                  setState(() {
                                    selectedCommand = value;
                                    showDateTimePicker =
                                        true; // Show date picker immediately
                                  });
                                }
                              },
                              activeColor:
                                  FlutterFlowTheme.of(context).secondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Date/Time Selection Section - Appears automatically after command selection
                    if (showDateTimePicker) ...[
                      SizedBox(height: 20),
                      Text(
                        FFLocalizations.of(context)
                            .getText('schedule_date_time'),
                        style: TextStyle(
                          fontFamily: 'PorscheNumber',
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: FlutterFlowTheme.of(context).primaryText,
                        ),
                      ),
                      SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color:
                              FlutterFlowTheme.of(context).secondaryBackground,
                          border: Border.all(
                              color: FlutterFlowTheme.of(context).lineColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Date Selection
                            Row(
                              children: [
                                Icon(Icons.calendar_today,
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryColor),
                                SizedBox(width: 8),
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () async {
                                      final DateTime? pickedDate =
                                          await showDatePicker(
                                        context: context,
                                        initialDate: selectedDateTime,
                                        firstDate: DateTime.now(),
                                        lastDate: DateTime.now()
                                            .add(Duration(days: 365)),
                                        builder: (BuildContext context,
                                            Widget? child) {
                                          final isDark =
                                              Theme.of(context).brightness ==
                                                  Brightness.dark;
                                          return Theme(
                                            data: isDark
                                                ? ThemeData.dark().copyWith(
                                                    colorScheme:
                                                        ColorScheme.dark(
                                                      primary:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .secondaryColor,
                                                      onPrimary:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .primaryBtnText,
                                                      surface: FlutterFlowTheme
                                                              .of(context)
                                                          .secondaryBackground,
                                                      onSurface:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .primaryText,
                                                    ),
                                                  )
                                                : ThemeData.light().copyWith(
                                                    colorScheme:
                                                        ColorScheme.light(
                                                      primary:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .secondaryColor,
                                                      onPrimary:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .primaryBtnText,
                                                      surface: FlutterFlowTheme
                                                              .of(context)
                                                          .secondaryBackground,
                                                      onSurface:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .primaryText,
                                                    ),
                                                  ),
                                            child: child!,
                                          );
                                        },
                                      );
                                      if (pickedDate != null) {
                                        setState(() {
                                          selectedDateTime = DateTime(
                                            pickedDate.year,
                                            pickedDate.month,
                                            pickedDate.day,
                                            selectedDateTime.hour,
                                            selectedDateTime.minute,
                                          );
                                        });
                                      }
                                    },
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 8, horizontal: 12),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                            color: FlutterFlowTheme.of(context)
                                                .lineColor),
                                        borderRadius: BorderRadius.circular(4),
                                        color: FlutterFlowTheme.of(context)
                                            .primaryBackground,
                                      ),
                                      child: Text(
                                        '${selectedDateTime.day}/${selectedDateTime.month}/${selectedDateTime.year}',
                                        style: TextStyle(
                                          fontFamily: 'PorscheNumber',
                                          color: FlutterFlowTheme.of(context)
                                              .primaryText,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 12),
                            // Time Selection
                            Row(
                              children: [
                                Icon(Icons.access_time,
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryColor),
                                SizedBox(width: 8),
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () async {
                                      final TimeOfDay? pickedTime =
                                          await showTimePicker(
                                        context: context,
                                        initialTime: TimeOfDay.fromDateTime(
                                            selectedDateTime),
                                        builder: (BuildContext context,
                                            Widget? child) {
                                          final isDark =
                                              Theme.of(context).brightness ==
                                                  Brightness.dark;
                                          return Theme(
                                            data: isDark
                                                ? ThemeData.dark().copyWith(
                                                    colorScheme:
                                                        ColorScheme.dark(
                                                      primary:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .secondaryColor,
                                                      onPrimary:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .primaryBtnText,
                                                      surface: FlutterFlowTheme
                                                              .of(context)
                                                          .secondaryBackground,
                                                      onSurface:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .primaryText,
                                                    ),
                                                  )
                                                : ThemeData.light().copyWith(
                                                    colorScheme:
                                                        ColorScheme.light(
                                                      primary:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .secondaryColor,
                                                      onPrimary:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .primaryBtnText,
                                                      surface: FlutterFlowTheme
                                                              .of(context)
                                                          .secondaryBackground,
                                                      onSurface:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .primaryText,
                                                    ),
                                                  ),
                                            child: child!,
                                          );
                                        },
                                      );
                                      if (pickedTime != null) {
                                        final newDateTime = DateTime(
                                          selectedDateTime.year,
                                          selectedDateTime.month,
                                          selectedDateTime.day,
                                          pickedTime.hour,
                                          pickedTime.minute,
                                        );

                                        // Validate that the selected time is in the future
                                        if (newDateTime
                                            .isBefore(DateTime.now())) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                FFLocalizations.of(context)
                                                    .getText(
                                                        'future_date_required'),
                                              ),
                                              backgroundColor:
                                                  FlutterFlowTheme.of(context)
                                                      .alternate,
                                            ),
                                          );
                                        } else {
                                          setState(() {
                                            selectedDateTime = newDateTime;
                                          });
                                        }
                                      }
                                    },
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 8, horizontal: 12),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                            color: FlutterFlowTheme.of(context)
                                                .lineColor),
                                        borderRadius: BorderRadius.circular(4),
                                        color: FlutterFlowTheme.of(context)
                                            .primaryBackground,
                                      ),
                                      child: Text(
                                        '${selectedDateTime.hour.toString().padLeft(2, '0')}:${selectedDateTime.minute.toString().padLeft(2, '0')}',
                                        style: TextStyle(
                                          fontFamily: 'PorscheNumber',
                                          color: FlutterFlowTheme.of(context)
                                              .primaryText,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 8),
                            // Visual feedback about scheduling
                            Container(
                              padding: EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: selectedDateTime.isBefore(DateTime.now())
                                    ? FlutterFlowTheme.of(context)
                                        .alternate
                                        .withValues(alpha: 0.1)
                                    : FlutterFlowTheme.of(context)
                                        .secondaryColor
                                        .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(
                                  color: selectedDateTime
                                          .isBefore(DateTime.now())
                                      ? FlutterFlowTheme.of(context).alternate
                                      : FlutterFlowTheme.of(context)
                                          .secondaryColor,
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    selectedDateTime.isBefore(DateTime.now())
                                        ? Icons.warning
                                        : Icons.check_circle,
                                    color: selectedDateTime
                                            .isBefore(DateTime.now())
                                        ? FlutterFlowTheme.of(context).alternate
                                        : FlutterFlowTheme.of(context)
                                            .secondaryColor,
                                    size: 16,
                                  ),
                                  SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      selectedDateTime.isBefore(DateTime.now())
                                          ? FFLocalizations.of(context)
                                              .getText('selected_time_past')
                                          : '${FFLocalizations.of(context).getText('command_will_execute')} ${selectedDateTime.day}/${selectedDateTime.month} ${FFLocalizations.of(context).getText('at_time')} ${selectedDateTime.hour.toString().padLeft(2, '0')}:${selectedDateTime.minute.toString().padLeft(2, '0')}',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: selectedDateTime
                                                .isBefore(DateTime.now())
                                            ? FlutterFlowTheme.of(context)
                                                .alternate
                                            : FlutterFlowTheme.of(context)
                                                .secondaryColor,
                                        fontFamily: 'PorscheNumber',
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text('Cancel'),
                ),
                TextButton(
                  onPressed: showDateTimePicker &&
                          !selectedDateTime.isBefore(DateTime.now())
                      ? () async {
                          Navigator.pop(context);
                          await _createScheduledCommand(
                              selectedDateTime, selectedCommand);
                        }
                      : null,
                  child: Text(
                    'Schedule Command',
                    style: TextStyle(
                      color: showDateTimePicker &&
                              !selectedDateTime.isBefore(DateTime.now())
                          ? FlutterFlowTheme.of(context).secondaryColor
                          : Colors.grey,
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Fetch scheduled commands from API
  Future<void> _fetchScheduledCommands() async {
    try {
      final token = widget.user.token;

      // Log request details
      print('FETCH REQUEST URL: $API_HOST/api/scheduledCommand/');
      print(
          'FETCH REQUEST HEADERS: {Content-Type: application/json, Authorization: Bearer ${token.substring(0, 10)}...}');

      final response = await http.get(
        Uri.parse('$API_HOST/api/scheduledCommand/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      // Log response details
      print('FETCH RESPONSE STATUS: ${response.statusCode}');
      print('FETCH RESPONSE BODY: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data['success'] == true) {
          setState(() {
            scheduledCommands = List<Map<String, dynamic>>.from(data['data']);
          });
          print('Fetched ${scheduledCommands.length} scheduled commands');
        } else {
          print('API returned success: false when fetching commands');
        }
      } else {
        print('Failed to fetch scheduled commands: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching scheduled commands: $e');
    }
  }

  // Create a new scheduled command with specified command type
  Future<void> _createScheduledCommand(
      DateTime scheduledTime, String commandType) async {
    try {
      final token = widget.user.token;
      final deviceNumber = widget.user.device?.deviceNumber ?? '';

      if (deviceNumber.isEmpty) {
        print('Error: Device number not found');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Device number not found')),
        );
        return;
      }

      // Create request body
      final requestBody = {
        "deviceNumber": deviceNumber,
        "command": commandType, // Use the selected command type
        "scheduledTime": scheduledTime.toIso8601String(),
        "isRecurring": false,
        "topic": deviceNumber
      };

      // Log request details
      print('REQUEST URL: $API_HOST/api/scheduledCommand/create');
      print(
          'REQUEST HEADERS: {Content-Type: application/json, Authorization: Bearer ${token.substring(0, 10)}...}');
      print('REQUEST BODY: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse('$API_HOST/api/scheduledCommand/create'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestBody),
      );

      // Log response details
      print('RESPONSE STATUS: ${response.statusCode}');
      print('RESPONSE BODY: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data['success'] == true) {
          print(
              'Command scheduled successfully: ${data['scheduleId'] ?? 'ID not provided'}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Action scheduled for ${_formatDateTime(scheduledTime)}',
                style: TextStyle(fontFamily: 'PorscheNumber'),
              ),
              duration: Duration(seconds: 3),
            ),
          );

          // Refresh the list of scheduled commands
          await _fetchScheduledCommands();
        } else {
          // Handle case where success is false but status code is 200/201
          String message = data['message'] ?? 'Unknown error occurred';
          print('API returned success: false with message: $message');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to schedule command: $message')),
          );
        }
      } else {
        // Handle other status codes
        print('API returned error status code: ${response.statusCode}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content:
                  Text('Failed to schedule command: ${response.statusCode}')),
        );
      }
    } catch (e) {
      print('Exception during command scheduling: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error scheduling command: $e')),
      );
    }
  }

  // Delete a scheduled command
  Future<void> _deleteScheduledCommand(String commandId) async {
    try {
      final token = widget.user.token;

      // Log request details
      print('DELETE REQUEST URL: $API_HOST/api/scheduledCommand/$commandId');
      print(
          'DELETE REQUEST HEADERS: {Content-Type: application/json, Authorization: Bearer ${token.substring(0, 10)}...}');

      final response = await http.delete(
        Uri.parse('$API_HOST/api/scheduledCommand/$commandId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      // Log response details
      print('DELETE RESPONSE STATUS: ${response.statusCode}');
      print('DELETE RESPONSE BODY: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data['success'] == true) {
          print('Command deleted successfully: $commandId');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Scheduled command deleted successfully',
                style: TextStyle(fontFamily: 'PorscheNumber'),
              ),
              duration: Duration(seconds: 3),
            ),
          );

          // Refresh the list
          await _fetchScheduledCommands();
        } else {
          String message = data['message'] ?? 'Unknown error occurred';
          print('API returned success: false with message: $message');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to delete command: $message')),
          );
        }
      } else {
        print('API returned error status code: ${response.statusCode}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content:
                  Text('Failed to delete command: ${response.statusCode}')),
        );
      }
    } catch (e) {
      print('Exception during command deletion: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting command: $e')),
      );
    }
  }

  // Get color based on command status
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'failed':
        return Colors.red;
      case 'skipped':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    Color buttonColor = showSuccess ? Colors.green : Color(0x6939D2C0);

    return GestureDetector(
      onVerticalDragUpdate: _onScroll,
      onLongPress: _onButtonPressed,
      onLongPressUp: _onButtonReleased,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: buttonColor,
          borderRadius: BorderRadius.circular(40),
          border: Border.all(
            color: FlutterFlowTheme.of(context).secondaryColor,
            width: 1,
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            Icon(
              currentIcon!,
              color: FlutterFlowTheme.of(context).primaryText,
              size: 30,
            ),
            Positioned.fill(
              child: AnimatedBuilder(
                animation: _controller!,
                builder: (context, child) {
                  return CircularProgressIndicator(
                    value: _controller!.value,
                    backgroundColor: buttonColor,
                    valueColor: AlwaysStoppedAnimation<Color>(
                        _controller!.isAnimating ? Colors.green : buttonColor),
                    strokeWidth: 6.0,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller!.dispose();
    _successAnimationController.dispose();
    super.dispose();
  }
}
