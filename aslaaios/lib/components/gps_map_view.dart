import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as google_maps;
import 'package:aslaa/flutter_flow/flutter_flow_google_map.dart'
    as ff_google_map;
import 'package:aslaa/flutter_flow/flutter_flow_theme.dart';
import 'package:aslaa/models/gps_history.dart';

class GpsMapView extends StatefulWidget {
  final List<GpsHistoryPoint> gpsPoints;

  const GpsMapView({
    Key? key,
    required this.gpsPoints,
  }) : super(key: key);

  @override
  _GpsMapViewState createState() => _GpsMapViewState();
}

class _GpsMapViewState extends State<GpsMapView> {
  final Completer<google_maps.GoogleMapController> _controller = Completer();
  Set<google_maps.Marker> _markers = {};
  Set<google_maps.Polyline> _polylines = {};

  ff_google_map.LatLng? _mapCenter;

  @override
  void initState() {
    super.initState();
    _setupMapData();
  }

  @override
  void didUpdateWidget(GpsMapView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.gpsPoints != oldWidget.gpsPoints) {
      _setupMapData();
    }
  }

  void _setupMapData() {
    if (widget.gpsPoints.isEmpty) return;

    _createMarkers();
    _createPolylines();
    _setMapCenter();
  }

  void _createMarkers() {
    _markers.clear();

    for (int i = 0; i < widget.gpsPoints.length; i++) {
      final point = widget.gpsPoints[i];
      final isFirst = i == 0;
      final isLast = i == widget.gpsPoints.length - 1;

      _markers.add(
        google_maps.Marker(
          markerId: google_maps.MarkerId(point.id),
          position: google_maps.LatLng(point.lat, point.lng),
          icon: _getMarkerIcon(point.speed, isFirst, isLast),
          infoWindow: google_maps.InfoWindow(
            title: point.formattedTime,
            snippet: '${point.formattedSpeed} • ${point.coordinatesString}',
          ),
          onTap: () {
            _showPointDetails(point);
          },
        ),
      );
    }
  }

  google_maps.BitmapDescriptor _getMarkerIcon(
      double speed, bool isFirst, bool isLast) {
    if (isFirst) {
      return google_maps.BitmapDescriptor.defaultMarkerWithHue(
          google_maps.BitmapDescriptor.hueGreen);
    } else if (isLast) {
      return google_maps.BitmapDescriptor.defaultMarkerWithHue(
          google_maps.BitmapDescriptor.hueRed);
    } else if (speed == 0) {
      return google_maps.BitmapDescriptor.defaultMarkerWithHue(
          google_maps.BitmapDescriptor.hueYellow);
    } else if (speed < 30) {
      return google_maps.BitmapDescriptor.defaultMarkerWithHue(
          google_maps.BitmapDescriptor.hueBlue);
    } else if (speed < 60) {
      return google_maps.BitmapDescriptor.defaultMarkerWithHue(
          google_maps.BitmapDescriptor.hueOrange);
    } else {
      return google_maps.BitmapDescriptor.defaultMarkerWithHue(
          google_maps.BitmapDescriptor.hueViolet);
    }
  }

  void _createPolylines() {
    _polylines.clear();

    if (widget.gpsPoints.length < 2) return;

    List<google_maps.LatLng> polylineCoordinates = widget.gpsPoints
        .map((point) => google_maps.LatLng(point.lat, point.lng))
        .toList();

    _polylines.add(
      google_maps.Polyline(
        polylineId: google_maps.PolylineId('gps_route'),
        points: polylineCoordinates,
        color: FlutterFlowTheme.of(context).primaryColor,
        width: 3,
        patterns: [],
      ),
    );
  }

  void _setMapCenter() {
    if (widget.gpsPoints.isNotEmpty) {
      final firstPoint = widget.gpsPoints.first;
      _mapCenter = ff_google_map.LatLng(firstPoint.lat, firstPoint.lng);
    }
  }

  void _showPointDetails(GpsHistoryPoint point) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).secondaryBackground,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Handle bar
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).alternate,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            SizedBox(height: 20),
            // Title
            Text(
              'GPS Point Details',
              style: FlutterFlowTheme.of(context).title3.override(
                    fontFamily: 'PorscheNumber',
                    fontWeight: FontWeight.bold,
                  ),
            ),
            SizedBox(height: 16),
            // Details
            _buildDetailItem(
                Icons.access_time, 'Time', point.formattedDateTime),
            _buildDetailItem(Icons.speed, 'Speed', point.formattedSpeed),
            _buildDetailItem(
                Icons.location_on, 'Coordinates', point.coordinatesString),
            if (point.address != null && point.address!.isNotEmpty)
              _buildDetailItem(Icons.place, 'Address', point.address!),
            SizedBox(height: 20),
            // Close button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: FlutterFlowTheme.of(context).primaryColor,
                  padding: EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Close',
                  style: TextStyle(
                    color: Colors.white,
                    fontFamily: 'PorscheNumber',
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(IconData icon, String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: FlutterFlowTheme.of(context).primaryColor,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: FlutterFlowTheme.of(context).bodyText2.override(
                        fontFamily: 'PorscheNumber',
                        color: FlutterFlowTheme.of(context).secondaryText,
                        fontWeight: FontWeight.w500,
                      ),
                ),
                Text(
                  value,
                  style: FlutterFlowTheme.of(context).bodyText1.override(
                        fontFamily: 'PorscheNumber',
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.gpsPoints.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.map,
              size: 64,
              color: FlutterFlowTheme.of(context).secondaryText,
            ),
            SizedBox(height: 16),
            Text(
              'No GPS data to display on map',
              style: FlutterFlowTheme.of(context).bodyText1.override(
                    fontFamily: 'PorscheNumber',
                    color: FlutterFlowTheme.of(context).secondaryText,
                  ),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        google_maps.GoogleMap(
          onMapCreated: (google_maps.GoogleMapController controller) {
            _controller.complete(controller);
          },
          initialCameraPosition: google_maps.CameraPosition(
            target: google_maps.LatLng(
              _mapCenter?.latitude ?? widget.gpsPoints.first.lat,
              _mapCenter?.longitude ?? widget.gpsPoints.first.lng,
            ),
            zoom: 15,
          ),
          markers: _markers,
          polylines: _polylines,
          mapType: google_maps.MapType.normal,
          myLocationEnabled: false,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: true,
          compassEnabled: true,
          mapToolbarEnabled: false,
        ),
        // Legend
        Positioned(
          top: 16,
          right: 16,
          child: Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).secondaryBackground,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Legend',
                  style: FlutterFlowTheme.of(context).bodyText2.override(
                        fontFamily: 'PorscheNumber',
                        fontWeight: FontWeight.bold,
                      ),
                ),
                SizedBox(height: 8),
                _buildLegendItem(Colors.green, 'Start'),
                _buildLegendItem(Colors.red, 'End'),
                _buildLegendItem(Colors.blue, '< 30 km/h'),
                _buildLegendItem(Colors.orange, '30-60 km/h'),
                _buildLegendItem(Colors.purple, '> 60 km/h'),
                _buildLegendItem(Colors.grey, 'Stopped'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(Color color, String label) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 6),
          Text(
            label,
            style: FlutterFlowTheme.of(context).bodyText2.override(
                  fontFamily: 'PorscheNumber',
                  fontSize: 10,
                ),
          ),
        ],
      ),
    );
  }
}
