import os
import logging
from flask import Flask
from dotenv import load_dotenv
from bot.mcp_server import init_app

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_app():
    """Create and configure the Flask application"""
    app = Flask(__name__)
    
    # Initialize the MCP server
    init_app(app)
    
    return app

if __name__ == "__main__":
    app = create_app()
    port = int(os.getenv("PORT", "5001"))  # Changed default from "5001"
    debug = os.getenv("FLASK_ENV") == "development"
    
    app.run(host="0.0.0.0", port=port, debug=debug)
